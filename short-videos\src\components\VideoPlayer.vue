<template>
  <div class="video-player" ref="playerRef">
    <video
      ref="videoRef"
      :src="video.url"
      :poster="video.cover"
      loop
      preload="none"
      loading="lazy"
      @click="togglePlay"
      @timeupdate="onTimeUpdate"
    />
    
    <div class="video-controls" v-show="showControls">
      <div class="play-pause" @click="togglePlay">
        <PauseOutlined v-if="isPlaying" />
        <PlayOutlined v-else />
      </div>
      
      <div class="progress-bar" @click="onProgressClick">
        <div class="progress" :style="{ width: progress + '%' }" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
  PlayCircleOutlined as PlayOutlined,
  PauseCircleOutlined as PauseOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  video: {
    type: Object,
    required: true
  }
})

const videoRef = ref(null)
const playerRef = ref(null)
const isPlaying = ref(false)
const progress = ref(0)
const showControls = ref(false)

let hideControlsTimer
let observer

const togglePlay = () => {
  if (videoRef.value.paused) {
    videoRef.value.play()
    isPlaying.value = true
  } else {
    videoRef.value.pause()
    isPlaying.value = false
  }
}

const onTimeUpdate = () => {
  const video = videoRef.value
  progress.value = (video.currentTime / video.duration) * 100
}

const onProgressClick = (e) => {
  const bar = e.currentTarget
  const pos = (e.pageX - bar.offsetLeft) / bar.offsetWidth
  videoRef.value.currentTime = pos * videoRef.value.duration
}

const showControlsTemp = () => {
  showControls.value = true
  clearTimeout(hideControlsTimer)
  hideControlsTimer = setTimeout(() => {
    showControls.value = false
  }, 3000)
}

const setupIntersectionObserver = () => {
  observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (!entry.isIntersecting && !videoRef.value.paused) {
        videoRef.value.pause()
        isPlaying.value = false
      }
    })
  }, {
    threshold: 0.5
  })
  
  observer.observe(playerRef.value)
}

onMounted(() => {
  playerRef.value.addEventListener('mousemove', showControlsTemp)
  setupIntersectionObserver()
})

onUnmounted(() => {
  playerRef.value?.removeEventListener('mousemove', showControlsTemp)
  clearTimeout(hideControlsTimer)
  observer?.disconnect()
})
</script>

<style scoped>
.video-player {
  position: relative;
  width: 100%;
  background: #000;
  aspect-ratio: 9/16;
}

video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(transparent, rgba(0,0,0,0.5));
}

.play-pause {
  width: 40px;
  height: 40px;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255,255,255,0.3);
  cursor: pointer;
}

.progress {
  height: 100%;
  background: #fe2c55;
  transition: width 0.1s;
}
</style> 