const canvas = document.getElementById("codeRain");
const ctx = canvas.getContext("2d");

// 设置画布尺寸为窗口大小
function setCanvasSize() {
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
}
setCanvasSize();
window.addEventListener("resize", setCanvasSize);

// 创建字符数组
const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
const fontSize = 16;
const columns = Math.floor(canvas.width / fontSize);

// 初始化每列的Y坐标
const drops = new Array(columns).fill(1);

// 绘制代码雨
function draw() {
  // 设置半透明黑色背景，形成拖尾效果
  ctx.fillStyle = "rgba(0, 0, 0, 0.05)";
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 设置文字样式
  ctx.fillStyle = "#0F0";
  ctx.font = fontSize + "px monospace";

  // 循环绘制每一列
  for (let i = 0; i < drops.length; i++) {
    // 随机选择一个字符
    const char = chars[Math.floor(Math.random() * chars.length)];

    // 绘制字符
    ctx.fillText(char, i * fontSize, drops[i] * fontSize);

    // 当字符到达底部或随机重置时，重置到顶部
    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
      drops[i] = 0;
    }

    // 移动字符
    drops[i]++;
  }
}

// 动画循环
setInterval(draw, 33);
