<template>
  <a-layout class="app-container">
    <a-layout-header class="header">
      <div class="nav-content">
        <div class="logo">
          <h1>短视频</h1>
        </div>
        <a-input-search
          v-model="searchText"
          placeholder="搜索视频"
          style="width: 300px"
          @search="onSearch"
        />
        <div class="user-actions">
          <a-button type="primary" @click="showUploadModal">
            <template #icon><UploadOutlined /></template>
            上传
          </a-button>
          <a-avatar :src="userAvatar" />
        </div>
      </div>
    </a-layout-header>

    <a-layout-content class="main-content">
      <video-feed />
    </a-layout-content>

    <!-- 上传视频模态框 -->
    <a-modal
      :visible="uploadModalVisible"
      @update:visible="uploadModalVisible = $event"
      title="上传视频"
      @ok="handleUpload"
      :confirmLoading="uploading"
      @cancel="uploadModalVisible = false"
      :keyboard="true"
      :maskClosable="false"
      destroyOnClose
    >
      <a-upload-dragger
        :fileList="fileList"
        :beforeUpload="beforeUpload"
        :maxCount="1"
        accept="video/*"
        @change="handleChange"
        :multiple="false"
        :showUploadList="true"
        :disabled="uploading"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">点击或拖拽视频文件上传</p>
        <p class="ant-upload-hint">支持的视频格式：MP4, WebM, AVI等</p>
      </a-upload-dragger>
    </a-modal>
  </a-layout>
</template>

<script setup>
import { ref } from 'vue'
import { UploadOutlined, InboxOutlined } from '@ant-design/icons-vue'
import { message, Upload, Modal } from 'ant-design-vue'
import VideoFeed from './components/VideoFeed.vue'
import { useVideoStore } from './stores/video'
import { uploadVideo } from './api/video'

const searchText = ref('')
const uploadModalVisible = ref(false)
const fileList = ref([])
const uploading = ref(false)
const userAvatar = ref('/images/avatars/default-avatar.png')
const videoStore = useVideoStore()

const onSearch = (value) => {
  videoStore.searchVideos(value)
}

const showUploadModal = () => {
  console.log('Modal visible:', uploadModalVisible.value)
  uploadModalVisible.value = true
}

const beforeUpload = (file) => {
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    message.error('只能上传视频文件!')
    return Upload.LIST_IGNORE
  }
  const isLt500M = file.size / 1024 / 1024 < 500
  if (!isLt500M) {
    message.error('视频大小不能超过 500MB!')
    return Upload.LIST_IGNORE
  }
  return isVideo
}

const handleChange = (info) => {
  const { status } = info.file
  fileList.value = info.fileList
  
  if (status === 'done') {
    message.success(`${info.file.name} 文件上传成功`)
  } else if (status === 'error') {
    message.error(`${info.file.name} 文件上传失败`)
  }
}

const handleUpload = async () => {
  if (fileList.value.length === 0) {
    message.warning('请选择要上传的视频文件')
    return
  }

  uploading.value = true
  try {
    const formData = new FormData()
    formData.append('file', fileList.value[0].originFileObj)

    const res = await uploadVideo(formData)
    if (res.code === 0) {
      message.success('视频上传成功!')
      uploadModalVisible.value = false
      fileList.value = []
      videoStore.fetchVideos()
    } else {
      message.error(res.message || '上传失败')
    }
  } catch (error) {
    message.error('上传失败，请重试')
  } finally {
    uploading.value = false
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: #f0f2f5;
}

:deep(.ant-modal) {
  top: 50%;
  transform: translateY(-50%);
}

:deep(.ant-modal-content) {
  border-radius: 8px;
}

.header {
  position: fixed;
  width: 100%;
  z-index: 1;
  padding: 0;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.logo h1 {
  color: #fe2c55;
  margin: 0;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.main-content {
  margin-top: 64px;
  padding: 24px;
  background: #f0f2f5;
}

:deep(.ant-upload-drag) {
  border-radius: 8px;
  border-color: #d9d9d9;
}

:deep(.ant-upload-list) {
  margin-top: 16px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
}
</style> 