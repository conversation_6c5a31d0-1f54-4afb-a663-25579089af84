{"root": true, "extends": ["eslint:recommended", "plugin:vue/essential", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier/@typescript-eslint", "plugin:prettier/recommended"], "globals": {"process": true}, "parser": "vue-eslint-parser", "parserOptions": {"parser": "@typescript-eslint/parser"}, "rules": {"@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/explicit-module-boundary-types": 0}}