import { defineStore } from "pinia";
import { ref } from "vue";
import { getVideoList, likeVideo, followUser } from "../api/video";

export const useVideoStore = defineStore("video", () => {
  const videos = ref([]);
  const loading = ref(false);
  const hasMore = ref(true);
  const page = ref(1);
  const pageSize = 5;

  const fetchVideos = async () => {
    if (loading.value) return;

    loading.value = true;
    try {
      const res = await getVideoList(page.value);
      if (page.value === 1) {
        videos.value = res.data;
      } else {
        videos.value.push(...res.data);
      }
      hasMore.value = res.hasMore;
      page.value++;
    } finally {
      loading.value = false;
    }
  };

  const searchVideos = async (keyword) => {
    videos.value = [];
    page.value = 1;
    hasMore.value = true;
    await fetchVideos();
  };

  const toggleLike = async (videoId) => {
    const video = videos.value.find((v) => v.id === videoId);
    if (!video) return;

    const oldStatus = video.isLiked;
    video.isLiked = !oldStatus;
    video.likes += oldStatus ? -1 : 1;

    try {
      await likeVideo(videoId, !oldStatus);
    } catch (error) {
      // 恢复原状态
      video.isLiked = oldStatus;
      video.likes += oldStatus ? 1 : -1;
    }
  };

  const toggleFollow = async (authorId) => {
    const videos = videos.value.filter((v) => v.authorId === authorId);
    if (videos.length === 0) return;

    const oldStatus = videos[0].isFollowing;
    videos.forEach((v) => {
      v.isFollowing = !oldStatus;
    });

    try {
      await followUser(authorId, !oldStatus);
    } catch (error) {
      videos.forEach((v) => {
        v.isFollowing = oldStatus;
      });
    }
  };

  return {
    videos,
    loading,
    hasMore,
    fetchVideos,
    searchVideos,
    toggleLike,
    toggleFollow,
  };
});
