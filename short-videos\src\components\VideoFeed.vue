<template>
  <div class="video-feed">
    <a-spin :spinning="loading">
      <video-card
        v-for="(video, index) in visibleVideos"
        :key="video.id"
        :video="video"
        :ref="el => { if (index === visibleVideos.length - 1) lastVideoRef = el }"
      />
      
      <a-empty v-if="videos.length === 0" />
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import VideoCard from './VideoCard.vue'
import { useVideoStore } from '../stores/video'
import { useIntersectionObserver } from '@vueuse/core'

const videoStore = useVideoStore()
const { videos, loading, hasMore } = storeToRefs(videoStore)
const pageSize = 1 // 每页显示的视频数量
const currentPage = ref(1)
const lastVideoRef = ref(null)

// 计算当前可见的视频
const visibleVideos = computed(() => {
  return videos.value.slice(0, currentPage.value * pageSize)
})

// 使用 useIntersectionObserver 监听最后一个视频
useIntersectionObserver(
  lastVideoRef,
  ([{ isIntersecting }]) => {
    if (isIntersecting && !loading.value && hasMore.value) {
      currentPage.value++
      videoStore.fetchVideos()
    }
  },
  { threshold: 0.5 }
)

onMounted(() => {
  videoStore.fetchVideos()
})
</script>

<style scoped>
.video-feed {
  max-width: 600px;
  margin: 0 auto;
  padding-bottom: 100px; /* 添加底部空间以便加载更多 */
}
</style> 