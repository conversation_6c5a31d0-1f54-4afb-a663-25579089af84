import { createApp } from "vue";
import { createPinia } from "pinia";
import "ant-design-vue/dist/antd.variable.min.css";

// 确保在开发环境中使用 mock
if (import.meta.env.DEV) {
  await import("./mock");
}

import {
  Button,
  Input,
  Avatar,
  Card,
  Layout,
  Modal as AModal,
  Upload as AUpload,
  Spin,
  Empty,
} from "ant-design-vue";
import App from "./App.vue";

const app = createApp(App);
const pinia = createPinia();

// 配置主题（可选）
import { ConfigProvider } from "ant-design-vue";
app.use(ConfigProvider);

// 注册需要的组件
app.use(Button);
app.use(Input);
app.use(Avatar);
app.use(Card);
app.use(Layout);
app.component("a-modal", AModal);
app.component("a-upload", AUpload);
app.component("a-upload-dragger", AUpload.Dragger);
app.use(pinia);
app.use(Spin);
app.use(Empty);

app.mount("#app");
