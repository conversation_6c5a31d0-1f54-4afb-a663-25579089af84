# vue-types

> Prop type definitions for [Vue.js](http://vuejs.org). Compatible with both Vue 1.x and 2.x

<p id="badges">
  <a href="https://www.npmjs.com/package/vue-types" target="_blank">
    <img alt="NPM package version" src="https://img.shields.io/npm/v/vue-types" />
  </a>
  <a href="https://circleci.com/gh/dwightjack/vue-types" target="_blank">
    <img alt="CircleCI status" src="https://circleci.com/gh/dwightjack/vue-types.svg?style=shield" />
  </a>
<a href="https://www.npmjs.com/package/vue-types" target="_blank">
    <img alt="Code coverage" src="https://img.shields.io/codeclimate/dwightjack/vue-types" />
  </a>

</p>

**Note: This document is for VueTypes 2 and above. If you are looking for an older version, refer to the `v1` branch.**

## Introduction

`vue-types` is a collection of configurable [prop type](http://vuejs.org/guide/components.html#Props) definitions for Vue.js components, inspired by React's `prop-types`.

[Try it now!](https://codesandbox.io/s/vue-types-2-demo-rbrdh) or learn more at the [official documentation site](https://dwightjack.github.io/vue-types/).

## Run examples

1. Install dependencies: `npm ci`
1. Run script: `npm run examples`
1. Wait for the server and bundler to start-up

## Contributing

1. Clone this repository
1. Install dependencies: `npm ci`
1. Make your changes
1. Update or add tests in `test/`
1. Verify that everything works: `npm run lint && npm test`
1. Submit a Pull Request

## License

[MIT](http://opensource.org/licenses/MIT)

Copyright (c) 2020 Marco Solazzi
