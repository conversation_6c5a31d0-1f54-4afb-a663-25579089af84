{"name": "short-videos", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^12.7.0", "ant-design-vue": "^4.0.0", "axios": "^1.6.2", "mockjs": "^1.1.0", "pinia": "^2.1.7", "vue": "^3.3.8"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0"}}