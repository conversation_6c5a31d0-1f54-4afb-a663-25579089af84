{"name": "vue-types", "version": "3.0.2", "description": "Prop types utility for Vue", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/dwightjack/vue-types", "main": "dist/vue-types.js", "source": ["src/index.ts", "src/shim.ts"], "amdName": "VueTypes", "unpkg": "dist/vue-types.umd.js", "umd:main": "dist/vue-types.umd.js", "module": "dist/vue-types.m.js", "esmodule": "dist/vue-types.modern.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.15.0"}, "files": ["dist", "src", "types/*.d.ts"], "scripts": {"prepublishOnly": "run-s lint test build", "build": "run-s build:clean build:copy build:cjs build:shim:cjs build:ts build:umd", "build:clean": "del dist", "build:copy": "cpy src/*.d.ts dist", "build:ts": "microbundle --external=vue --tsconfig=./tsconfig.build.json --format=modern,es", "build:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/index.cjs.ts -o dist/vue-types.js --no-pkg-main --format=cjs", "build:shim:cjs": "microbundle --external=vue --tsconfig=./tsconfig.build.json -i src/shim.cjs.ts -o dist/shim.js --no-pkg-main --format=cjs", "build:umd": "cross-env NODE_ENV=production microbundle --external=vue --tsconfig=./tsconfig.build.json --format=umd", "test": "karma start karma.conf.js --single-run --browsers ChromeHeadless", "test:saucelab": "karma start karma-sauce.conf.js --single-run", "lint": "eslint  '{src,test,.}/**/*.{ts,js}'", "examples": "cross-env NODE_ENV=development node ./scripts/run-examples.js", "docs:dev": "vuepress dev docs", "docs:build": "cross-env NODE_ENV=production vuepress build docs"}, "keywords": ["vue", "props"], "repository": {"type": "git", "url": "https://github.com/dwightjack/vue-types.git"}, "bugs": "https://github.com/dwightjack/vue-types/issues", "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.10.4", "@babel/plugin-proposal-optional-chaining": "7.10.4", "@types/jasmine": "3.5.11", "@types/node": "14.0.23", "@typescript-eslint/eslint-plugin": "3.6.1", "@typescript-eslint/parser": "3.6.1", "babel-plugin-transform-node-env-inline": "0.4.3", "browser-sync": "2.26.12", "core-js": "3.6.5", "core-js-bundle": "3.6.5", "cpy-cli": "3.1.1", "cross-env": "7.0.2", "del": "5.1.0", "del-cli": "3.0.1", "eslint": "7.4.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "6.2.2", "jasmine": "3.5.0", "karma": "5.1.0", "karma-chrome-launcher": "3.1.0", "karma-jasmine": "3.3.1", "karma-sauce-launcher": "4.1.4", "karma-typescript": "5.0.3", "karma-typescript-es6-transform": "5.0.3", "microbundle": "0.12.3", "npm-run-all": "4.1.5", "prettier": "2.0.5", "puppeteer": "5.1.0", "typescript": "3.9.6", "vue": "3.0.0-rc.7", "vue-class-component": "7.2.3", "vuepress": "1.5.2"}, "dependencies": {"is-plain-object": "3.0.1"}, "browserslist": ["last 3 versions", "Safari >= 10", "not ie <= 8", "Edge >= 12", "iOS >= 10", "Android >= 4.4"]}