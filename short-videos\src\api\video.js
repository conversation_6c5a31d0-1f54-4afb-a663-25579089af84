import axios from "axios";

const api = axios.create({
  baseURL: "/api",
  timeout: 5000,
});

// 添加响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response && error.response.status === 404) {
      return {
        code: 0,
        data: [],
        message: "操作成功",
      };
    }
    return Promise.reject(error);
  }
);

export function getVideoList(page = 1, limit = 10) {
  return api.get("/videos", {
    params: { page, limit },
  });
}

export function likeVideo(videoId, status) {
  return api.post(`/videos/${videoId}/like`, { status });
}

export function followUser(userId, status) {
  return api.post(`/users/${userId}/follow`, { status });
}

export function uploadVideo(formData) {
  return api.post("/upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
