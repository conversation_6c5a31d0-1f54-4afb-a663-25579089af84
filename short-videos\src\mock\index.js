import Mock from "mockjs";

// 设置延迟和请求超时时间
Mock.setup({
  timeout: "200-600",
});

// 生成视频列表数据
Mock.mock(new RegExp("/api/videos.*"), "get", (options) => {
  const params = new URLSearchParams(options.url.split("?")[1]);
  const page = parseInt(params.get("page")) || 1;
  const limit = parseInt(params.get("limit")) || 10;

  return {
    code: 0,
    data: Array(limit)
      .fill(null)
      .map((_, index) => ({
        id: Mock.Random.guid(),
        url: "https://www.w3schools.com/html/mov_bbb.mp4", // 使用一个实际可用的测试视频
        cover: Mock.Random.image(
          "600x800",
          Mock.Random.color(),
          Mock.Random.word()
        ),
        description: Mock.Random.paragraph(1, 3),
        likes: Mock.Random.integer(0, 100000),
        comments: Mock.Random.integer(0, 50000),
        isLiked: false,
        author: {
          id: Mock.Random.guid(),
          name: Mock.Random.cname(),
          avatar: Mock.Random.image(
            "100x100",
            Mock.Random.color(),
            Mock.Random.cword()
          ),
        },
        isFollowing: false,
      })),
    hasMore: page < 5,
  };
});

// 处理点赞请求
Mock.mock(new RegExp("/api/videos/.*?/like"), "post", {
  code: 0,
  message: "操作成功",
});

// 处理关注请求
Mock.mock(new RegExp("/api/users/.*?/follow"), "post", {
  code: 0,
  message: "操作成功",
});

// 处理文件上传请求
Mock.mock(new RegExp("/api/upload"), "post", {
  code: 0,
  data: {
    url: "https://www.w3schools.com/html/mov_bbb.mp4",
    cover: Mock.Random.image("600x800"),
  },
  message: "上传成功",
});
