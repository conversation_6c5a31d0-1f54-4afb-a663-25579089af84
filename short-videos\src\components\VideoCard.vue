<template>
  <a-card class="video-card" :bordered="false">
    <video-player :video="video" />
    
    <div class="video-info">
      <div class="author-info">
        <a-avatar :src="video.author.avatar" />
        <span class="author-name">{{ video.author.name }}</span>
        <a-button
          :type="video.isFollowing ? 'default' : 'primary'"
          size="small"
          @click="toggleFollow"
        >
          {{ video.isFollowing ? '已关注' : '关注' }}
        </a-button>
      </div>
      
      <p class="video-description">{{ video.description }}</p>
      
      <div class="interaction-buttons">
        <a-button
          type="text"
          class="interaction-btn"
          :class="{ 'is-liked': video.isLiked }"
          @click="toggleLike"
        >
          <template #icon>
            <HeartOutlined v-if="!video.isLiked" />
            <HeartFilled v-else style="color: #fe2c55" />
          </template>
          {{ formatCount(video.likes) }}
        </a-button>
        
        <a-button type="text" @click="showComments">
          <template #icon><CommentOutlined /></template>
          {{ formatCount(video.comments) }}
        </a-button>
        
        <a-button type="text" @click="shareVideo">
          <template #icon><ShareAltOutlined /></template>
          分享
        </a-button>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import {
  HeartOutlined,
  HeartFilled,
  CommentOutlined,
  ShareAltOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import VideoPlayer from './VideoPlayer.vue'
import { useVideoStore } from '../stores/video'

const props = defineProps({
  video: {
    type: Object,
    required: true
  }
})

const videoStore = useVideoStore()

const toggleLike = () => {
  videoStore.toggleLike(props.video.id)
}

const toggleFollow = () => {
  videoStore.toggleFollow(props.video.authorId)
}

const showComments = () => {
  // 显示评论列表
}

const shareVideo = () => {
  message.success('分享链接已复制')
}

const formatCount = (count) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  return count
}
</script>

<style scoped>
.video-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
}

.video-info {
  padding: 16px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.author-name {
  font-weight: 600;
}

.video-description {
  margin: 12px 0;
  color: #666;
}

.interaction-buttons {
  display: flex;
  gap: 24px;
  margin-top: 12px;
}

.interaction-btn.is-liked {
  color: #fe2c55;
}
</style> 